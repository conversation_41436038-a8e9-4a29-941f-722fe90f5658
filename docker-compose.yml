version: '3.8'

services:
  # Dify Main Service
  dify-api:
    image: langgenius/dify-api:1.7.1
    container_name: dify-api
    restart: always
    environment:
      MODE: api
      SECRET_KEY: ************************************************
      DB_USERNAME: postgres
      DB_PASSWORD: difyai123456
      DB_HOST: dify-db
      DB_PORT: 5432
      DB_DATABASE: dify
      REDIS_HOST: dify-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: difyai123456
      CELERY_BROKER_URL: redis://:difyai123456@dify-redis:6379/1
      STORAGE_TYPE: local
      VECTOR_STORE: weaviate
      WEAVIATE_ENDPOINT: http://weaviate:8080
      WEAVIATE_API_KEY: WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih
    ports:
      - "5001:5001"
    depends_on:
      - dify-db
      - dify-redis
    volumes:
      - ./volumes/dify/storage:/app/api/storage
    networks:
      - lunwen-network

  dify-worker:
    image: langgenius/dify-api:1.7.1
    container_name: dify-worker
    restart: always
    environment:
      MODE: worker
      SECRET_KEY: ************************************************
      DB_USERNAME: postgres
      DB_PASSWORD: difyai123456
      DB_HOST: dify-db
      DB_PORT: 5432
      DB_DATABASE: dify
      REDIS_HOST: dify-redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: difyai123456
      CELERY_BROKER_URL: redis://:difyai123456@dify-redis:6379/1
      STORAGE_TYPE: local
      VECTOR_STORE: weaviate
      WEAVIATE_ENDPOINT: http://weaviate:8080
      WEAVIATE_API_KEY: WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih
    depends_on:
      - dify-db
      - dify-redis
    volumes:
      - ./volumes/dify/storage:/app/api/storage
    networks:
      - lunwen-network

  dify-web:
    image: langgenius/dify-web:1.7.1
    container_name: dify-web
    restart: always
    environment:
      CONSOLE_API_URL: http://localhost:5001
      APP_API_URL: http://localhost:5001
    ports:
      - "3000:3000"
    networks:
      - lunwen-network

  # Paper Editor API Service
  paper-editor-api:
    build:
      context: ./paper-editor-api
      dockerfile: Dockerfile
    container_name: paper-editor-api
    restart: always
    ports:
      - "8890:8890"
    depends_on:
      - paper-editor-postgres
    volumes:
      - ./paper-editor-api/uploads:/root/uploads
    networks:
      - lunwen-network

  # Paper Node Service
  paper-node-service:
    build:
      context: ./paper-node-service
      dockerfile: Dockerfile
    container_name: paper-node-service
    restart: always
    ports:
      - "9529:9529"
    networks:
      - lunwen-network

  # Paper Python Service
  paper-py-service:
    build:
      context: ./paper-py-service
      dockerfile: Dockerfile
    container_name: paper-py-service
    restart: always
    ports:
      - "9528:9528"
    networks:
      - lunwen-network

  # Databases
  dify-db:
    image: postgres:15-alpine
    container_name: dify-db
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: difyai123456
      POSTGRES_DB: dify
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ./volumes/dify/db:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD', 'pg_isready', '-h', 'dify-db', '-U', 'postgres', '-d', 'dify']
      interval: 1s
      timeout: 3s
      retries: 60
    networks:
      - lunwen-network

  paper-editor-postgres:
    image: postgres:15-alpine
    container_name: paper-editor-postgres
    restart: always
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: paper_editor
    ports:
      - "5432:5432"
    volumes:
      - ./volumes/paper-editor/postgres:/var/lib/postgresql/data
      - ./paper-editor-api/sql/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - lunwen-network

  # Redis
  dify-redis:
    image: redis:6-alpine
    container_name: dify-redis
    restart: always
    environment:
      REDISCLI_AUTH: difyai123456
    volumes:
      - ./volumes/dify/redis:/data
    command: redis-server --requirepass difyai123456
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
    networks:
      - lunwen-network

  # Vector Database
  weaviate:
    image: semitechnologies/weaviate:1.19.0
    container_name: weaviate
    restart: always
    volumes:
      - ./volumes/weaviate:/var/lib/weaviate
    environment:
      PERSISTENCE_DATA_PATH: /var/lib/weaviate
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: false
      DEFAULT_VECTORIZER_MODULE: none
      CLUSTER_HOSTNAME: node1
      AUTHENTICATION_APIKEY_ENABLED: true
      AUTHENTICATION_APIKEY_ALLOWED_KEYS: WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih
      AUTHENTICATION_APIKEY_USERS: <EMAIL>
      AUTHORIZATION_ADMINLIST_ENABLED: true
      AUTHORIZATION_ADMINLIST_USERS: <EMAIL>
    ports:
      - "8080:8080"
    networks:
      - lunwen-network

networks:
  lunwen-network:
    driver: bridge

volumes:
  dify-db-data:
  dify-redis-data:
  paper-editor-postgres-data:
  weaviate-data:
