# 论文生成服务启动脚本 (PowerShell)
# 启动 dify-main、paper-editor-api、paper-node-service、paper-py-service

Write-Host "🚀 开始启动论文生成服务..." -ForegroundColor Green

# 检查 Docker 是否安装
try {
    docker --version | Out-Null
    Write-Host "✅ Docker 已安装" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker 未安装，请先安装 Docker Desktop" -ForegroundColor Red
    exit 1
}

# 检查 Docker Compose 是否安装
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose 已安装" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose 未安装，请先安装 Docker Compose" -ForegroundColor Red
    exit 1
}

# 创建必要的目录
Write-Host "📁 创建数据目录..." -ForegroundColor Yellow

$directories = @(
    "volumes\dify\db",
    "volumes\dify\redis", 
    "volumes\dify\storage",
    "volumes\paper-editor\postgres",
    "volumes\weaviate",
    "paper-editor-api\uploads"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  创建目录: $dir" -ForegroundColor Gray
    }
}

Write-Host "🔧 构建和启动服务..." -ForegroundColor Yellow

# 构建并启动所有服务
try {
    docker-compose up -d --build
    Write-Host "✅ 服务构建和启动命令执行成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 服务启动失败: $_" -ForegroundColor Red
    exit 1
}

Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 检查服务状态
Write-Host "📊 检查服务状态..." -ForegroundColor Yellow
docker-compose ps

Write-Host ""
Write-Host "✅ 服务启动完成！" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 服务访问地址：" -ForegroundColor Cyan
Write-Host "  - Dify Web UI:        http://localhost:3000" -ForegroundColor White
Write-Host "  - Dify API:           http://localhost:5001" -ForegroundColor White
Write-Host "  - Paper Editor API:   http://localhost:8890" -ForegroundColor White
Write-Host "  - Paper Node Service: http://localhost:9529" -ForegroundColor White
Write-Host "  - Paper Py Service:   http://localhost:9528" -ForegroundColor White
Write-Host "  - Weaviate:           http://localhost:8080" -ForegroundColor White
Write-Host "  - PostgreSQL:         localhost:5432" -ForegroundColor White
Write-Host ""
Write-Host "📝 常用命令：" -ForegroundColor Cyan
Write-Host "  查看日志:    docker-compose logs -f [service_name]" -ForegroundColor White
Write-Host "  停止服务:    docker-compose down" -ForegroundColor White
Write-Host "  重启服务:    docker-compose restart [service_name]" -ForegroundColor White
Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
