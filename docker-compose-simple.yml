version: '3.8'

# 简化版 - 只包含四个核心服务
services:
  # Dify Main Service (简化版)
  dify-main:
    image: langgenius/dify-api:1.7.1
    container_name: dify-main
    restart: always
    environment:
      MODE: api
      SECRET_KEY: ************************************************
      # 使用 SQLite 简化数据库配置
      DB_USERNAME: ""
      DB_PASSWORD: ""
      DB_HOST: ""
      DB_PORT: ""
      DB_DATABASE: ""
      # 使用内存 Redis
      REDIS_HOST: ""
      REDIS_PORT: ""
      REDIS_PASSWORD: ""
      STORAGE_TYPE: local
      VECTOR_STORE: ""
    ports:
      - "5001:5001"
    volumes:
      - ./volumes/dify-simple:/app/api/storage
    networks:
      - simple-network

  # Paper Editor API Service
  paper-editor-api:
    build:
      context: ./paper-editor-api
      dockerfile: Dockerfile
    container_name: paper-editor-api
    restart: always
    ports:
      - "8890:8890"
    volumes:
      - ./paper-editor-api/uploads:/root/uploads
    networks:
      - simple-network

  # Paper Node Service
  paper-node-service:
    build:
      context: ./paper-node-service
      dockerfile: Dockerfile
    container_name: paper-node-service
    restart: always
    ports:
      - "9529:9529"
    networks:
      - simple-network

  # Paper Python Service
  paper-py-service:
    build:
      context: ./paper-py-service
      dockerfile: Dockerfile
    container_name: paper-py-service
    restart: always
    ports:
      - "9528:9528"
    networks:
      - simple-network

networks:
  simple-network:
    driver: bridge

volumes:
  dify-simple-storage:
